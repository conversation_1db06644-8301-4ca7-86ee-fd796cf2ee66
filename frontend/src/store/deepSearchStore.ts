import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { Message } from "@langchain/langgraph-sdk";
import { ProcessedEvent } from "@/components/deep-search/ActivityTimeline";

interface DeepSearchState {
  // 核心状态
  processedEventsTimeline: ProcessedEvent[];
  isSubmittingRequest: boolean;
  showProgressIndicator: boolean;
  currentResearchStage: string;
  fastForwardProgress: boolean;
  
  // 线程相关状态
  messages: Message[] | null;
  isLoading: boolean;
  
  // 初始查询状态
  initialQuery: string | null;
  
  // Actions
  setProcessedEventsTimeline: (events: ProcessedEvent[]) => void;
  addProcessedEvent: (event: ProcessedEvent) => void;
  setIsSubmittingRequest: (isSubmitting: boolean) => void;
  setShowProgressIndicator: (show: boolean) => void;
  setCurrentResearchStage: (stage: string) => void;
  setFastForwardProgress: (fastForward: boolean) => void;
  setMessages: (messages: Message[] | null) => void;
  setIsLoading: (loading: boolean) => void;
  setInitialQuery: (query: string | null) => void;
  
  // 重置状态
  resetState: () => void;
  clearTimeline: () => void;
}

const initialState = {
  processedEventsTimeline: [],
  isSubmittingRequest: false,
  showProgressIndicator: false,
  currentResearchStage: 'analysis',
  fastForwardProgress: false,
  messages: null,
  isLoading: false,
  initialQuery: null,
};

export const useDeepSearchStore = create<DeepSearchState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setProcessedEventsTimeline: (events) => set({ processedEventsTimeline: events }),
      
      addProcessedEvent: (event) => set((state) => ({
        processedEventsTimeline: [...state.processedEventsTimeline, event]
      })),
      
      setIsSubmittingRequest: (isSubmitting) => set({ isSubmittingRequest: isSubmitting }),
      
      setShowProgressIndicator: (show) => set({ showProgressIndicator: show }),
      
      setCurrentResearchStage: (stage) => set({ currentResearchStage: stage }),
      
      setFastForwardProgress: (fastForward) => set({ fastForwardProgress: fastForward }),
      
      setMessages: (messages) => set({ messages }),
      
      setIsLoading: (loading) => set({ isLoading }),
      
      setInitialQuery: (query) => set({ initialQuery: query }),
      
      resetState: () => set(initialState),
      
      clearTimeline: () => set({ processedEventsTimeline: [] }),
    }),
    {
      name: 'deep-search-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // 只持久化核心状态，不持久化临时状态
        processedEventsTimeline: state.processedEventsTimeline,
        messages: state.messages,
        currentResearchStage: state.currentResearchStage,
        // 不持久化 loading 状态和临时状态
      }),
    }
  )
);
